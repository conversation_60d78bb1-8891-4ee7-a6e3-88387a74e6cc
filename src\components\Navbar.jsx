import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, Globe, Plane, Car, FileText, Globe2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { useTranslation } from 'react-i18next';
import logo from '/sparkmars_logo.svg';

const Navbar = () => {
  const { t, i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navLinks = [
    { name: t('nav.home'), path: '/' },
    { name: t('nav.about'), path: '/about' },
    { name: t('nav.services'), path: '/services' },
    { name: t('nav.team'), path: '/team' },
    { name: t('nav.blog'), path: '/blog' },
    { name: t('nav.contact'), path: '/contact' },
  ];

  const serviceNavItems = [
    { id: 'flights', icon: Plane, title: t('services.flights'), path: '/services' },
    { id: 'car-rental', icon: Car, title: t('services.carRental'), path: '/services#car-rental' },
    { id: 'visa', icon: FileText, title: t('services.visaConsultant'), path: '/services#visa' },
    { id: 'immigration', icon: Globe2, title: t('services.immigration'), path: '/services#immigration' },
  ];

  const toggleLanguage = () => {
    const newLang = i18n.language === 'en' ? 'fr' : 'en';
    i18n.changeLanguage(newLang);
    toast({
      title: newLang === 'fr' ? '🇫🇷 Langue changée en français!' : '🇬🇧 Language changed to English!',
      duration: 3000,
    });
  };

  return (
    <motion.div
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={`fixed w-full z-50 transition-all duration-300 bg-white ${
        scrolled ? 'shadow-lg' : 'shadow-md'
      }`}
    >
      {/* Main navbar container with responsive padding */}
      <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
        {/* Mobile-optimized navbar height: smaller on mobile (h-14), larger on desktop (md:h-16) */}
        <div className="flex justify-between items-center h-14 md:h-16">
          <Link to="/" className="flex items-center space-x-2 sm:space-x-3">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="flex items-center"
            >
              <img
                src={logo}
                alt="Sparkmars Logo"
                className="h-12 sm:h-14 md:h-16 w-auto"
              />
            </motion.div>
          </Link>

          {/* Desktop navigation - hidden on mobile/tablet */}
          <div className="hidden lg:flex items-center space-x-4 xl:space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.path}
                to={link.path}
                className={`relative text-sm font-bold transition-colors text-[#11a7ed] hover:text-[#0d96d4] ${
                  location.pathname === link.path ? 'font-extrabold' : ''
                }`}
              >
                {link.name}
                {location.pathname === link.path && (
                  <motion.div
                    layoutId="navbar-indicator"
                    className="absolute -bottom-2 left-0 right-0 h-0.5 bg-[#11a7ed] rounded-full"
                  />
                )}
              </Link>
            ))}
            <Button
              onClick={toggleLanguage}
              variant="ghost"
              size="sm"
              className="flex items-center space-x-2 text-[#14597D] hover:text-[#0d3d54] hover:bg-[#F5F5F5]"
            >
              <Globe className="w-4 h-4" />
              <span>{i18n.language === 'en' ? 'EN' : 'FR'}</span>
            </Button>
          </div>

          {/* Mobile hamburger menu button - touch-friendly size (44x44px minimum) */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="lg:hidden text-[#14597D] hover:text-[#0d3d54] transition-colors p-2 -mr-2 touch-manipulation"
            aria-label={isOpen ? 'Close menu' : 'Open menu'}
          >
            {isOpen ? <X className="w-6 h-6 sm:w-7 sm:h-7" /> : <Menu className="w-6 h-6 sm:w-7 sm:h-7" />}
          </button>
        </div>

        {/* Service navigation - hidden on mobile, visible on desktop */}
        <div className="hidden lg:flex space-x-2 pt-2 pb-2 border-t border-gray-200 bg-[#e5e4e4] rounded-b-md">
          {serviceNavItems.map((item) => (
            <Link
              key={item.id}
              to={item.path}
              className={`flex items-center space-x-2 px-4 h-11 rounded-full text-sm font-semibold text-white bg-[#11a7ed] transition-all duration-200 hover:bg-[#0d96d4] hover:shadow-md hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0d96d4] ${location.pathname === '/services' && item.id === 'flights' ? 'bg-[#0d96d4] shadow-md' : ''}`}
            >
              <item.icon className="w-4 h-4 text-white" />
              <span>{item.title}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Mobile menu - full-screen overlay with touch-friendly targets */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="lg:hidden bg-white border-t border-gray-200 overflow-hidden"
          >
            {/* Mobile menu content with proper spacing and touch targets */}
            <div className="px-4 sm:px-6 py-6 space-y-1 max-h-[calc(100vh-3.5rem)] overflow-y-auto">
              {/* Main navigation links - touch-friendly spacing */}
              {navLinks.map((link) => (
                <Link
                  key={link.path}
                  to={link.path}
                  onClick={() => setIsOpen(false)}
                  className={`block py-3 px-2 text-base sm:text-lg font-bold transition-colors text-[#11a7ed] hover:text-[#0d96d4] rounded-lg hover:bg-[#F5F5F5] ${
                    location.pathname === link.path ? 'bg-[#F5F5F5] font-extrabold' : ''
                  }`}
                >
                  {link.name}
                </Link>
              ))}

              {/* Divider */}
              <div className="border-t border-gray-200 my-4"></div>

              {/* Service navigation items - touch-friendly */}
              {serviceNavItems.map((item) => (
                <Link
                  key={item.id}
                  to={item.path}
                  onClick={() => setIsOpen(false)}
                  className="flex items-center space-x-3 py-2.5 px-3 min-h-[44px] bg-[#11a7ed] text-white rounded-full transition-all duration-200 hover:bg-[#0d96d4] hover:shadow-md"
                >
                  <item.icon className="w-5 h-5 flex-shrink-0 text-white" />
                  <span className="text-base sm:text-lg font-semibold">{item.title}</span>
                </Link>
              ))}

              {/* Divider */}
              <div className="border-t border-gray-200 my-4"></div>

              {/* Language switcher - touch-friendly button */}
              <Button
                onClick={() => {
                  toggleLanguage();
                  setIsOpen(false);
                }}
                variant="ghost"
                className="w-full justify-start py-3 px-2 text-base sm:text-lg text-[#14597D] hover:text-[#0d3d54] hover:bg-[#F5F5F5] h-auto"
              >
                <Globe className="w-5 h-5 mr-3" />
                {i18n.language === 'en' ? t('nav.switchToFrench') : t('nav.switchToEnglish')}
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default Navbar;