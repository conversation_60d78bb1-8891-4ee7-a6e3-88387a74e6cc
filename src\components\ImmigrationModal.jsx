import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Globe2, Calendar, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { sendImmigrationEmail } from '@/services/emailService';

/**
 * Immigration Services Inquiry Modal Component
 * Allows users to submit immigration services inquiries that are sent via <NAME_EMAIL>
 */
const ImmigrationModal = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  // Form state management
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    currentCountry: '',
    destinationCountry: '',
    immigrationType: 'work',
    timeline: '',
    additionalInfo: '',
  });

  // Loading state for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);

  /**
   * Handle input changes and update form state
   */
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  /**
   * Validate form data before submission
   */
  const validateForm = () => {
    if (!formData.name.trim()) {
      toast({
        title: t('modals.immigration.validation.name'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!formData.email.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      toast({
        title: t('modals.immigration.validation.email'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!formData.phone.trim()) {
      toast({
        title: t('modals.immigration.validation.phone'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!formData.currentCountry.trim()) {
      toast({
        title: t('modals.immigration.validation.currentCountry'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!formData.destinationCountry.trim()) {
      toast({
        title: t('modals.immigration.validation.destinationCountry'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    return true;
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Set loading state
    setIsSubmitting(true);

    try {
      // Send email via EmailJS
      await sendImmigrationEmail(formData);

      // Show success message
      toast({
        title: t('modals.immigration.toasts.successTitle'),
        description: t('modals.immigration.toasts.successDesc'),
        duration: 5000,
      });

      // Reset form and close modal
      setFormData({
        name: '',
        email: '',
        phone: '',
        currentCountry: '',
        destinationCountry: '',
        immigrationType: 'work',
        timeline: '',
        additionalInfo: '',
      });
      onClose();
    } catch (error) {
      // Show error message
      toast({
        title: t('modals.immigration.toasts.errorTitle'),
        description: t('modals.immigration.toasts.errorDesc'),
        variant: 'destructive',
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Don't render if modal is not open
  if (!isOpen) return null;

  return (
    // Modal overlay with backdrop
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
      {/* Modal container with responsive width */}
      <div className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white rounded-2xl shadow-2xl">
        {/* Modal header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between rounded-t-2xl">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-[#011530]/10 flex items-center justify-center">
              <Globe2 className="w-5 h-5 text-[#011530]" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900">{t('modals.immigration.title')}</h2>
          </div>
          {/* Close button - touch-friendly size */}
          <button
            onClick={onClose}
            className="w-10 h-10 rounded-full hover:bg-gray-100 flex items-center justify-center transition-colors"
            aria-label="Close modal"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Modal body with form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-5">
          {/* Personal Information Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">{t('modals.immigration.personalInfo')}</h3>
            
            {/* Name field */}
            <div>
              <Label htmlFor="name" className="text-gray-700 mb-2 block font-medium">
                {t('modals.immigration.labels.name')}
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder={t('modals.immigration.placeholders.name')}
                className="bg-gray-50 border-gray-300 text-gray-800 h-11"
                required
              />
            </div>

            {/* Email and Phone in grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.immigration.labels.email')}
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder={t('modals.immigration.placeholders.email')}
                  className="bg-gray-50 border-gray-300 text-gray-800 h-11"
                  required
                />
              </div>
              <div>
                <Label htmlFor="phone" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.immigration.labels.phone')}
                </Label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder={t('modals.immigration.placeholders.phone')}
                  className="bg-gray-50 border-gray-300 text-gray-800 h-11"
                  required
                />
              </div>
            </div>
          </div>

          {/* Immigration Details Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">{t('modals.immigration.details')}</h3>
            
            {/* Current and Destination Countries */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="currentCountry" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.immigration.labels.currentCountry')}
                </Label>
                <div className="relative">
                  <Input
                    id="currentCountry"
                    name="currentCountry"
                    value={formData.currentCountry}
                    onChange={handleChange}
                    placeholder={t('modals.immigration.placeholders.currentCountry')}
                    className="bg-gray-50 border-gray-300 text-gray-800 h-11 pl-10"
                    required
                  />
                  <Globe2 className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
              </div>
              <div>
                <Label htmlFor="destinationCountry" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.immigration.labels.destinationCountry')}
                </Label>
                <div className="relative">
                  <Input
                    id="destinationCountry"
                    name="destinationCountry"
                    value={formData.destinationCountry}
                    onChange={handleChange}
                    placeholder={t('modals.immigration.placeholders.destinationCountry')}
                    className="bg-gray-50 border-gray-300 text-gray-800 h-11 pl-10"
                    required
                  />
                  <Globe2 className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>

            {/* Immigration Type and Timeline */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="immigrationType" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.immigration.labels.immigrationType')}
                </Label>
                <select
                  id="immigrationType"
                  name="immigrationType"
                  value={formData.immigrationType}
                  onChange={handleChange}
                  className="w-full h-11 px-3 bg-gray-50 border border-gray-300 rounded-md text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#011530]"
                  required
                >
                  <option value="work">Work Immigration</option>
                  <option value="family">Family Sponsorship</option>
                  <option value="investment">Investment/Business</option>
                  <option value="study">Study Immigration</option>
                  <option value="refugee">Refugee/Asylum</option>
                  <option value="permanent">Permanent Residency</option>
                  <option value="citizenship">Citizenship</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <Label htmlFor="timeline" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.immigration.labels.timeline')}
                </Label>
                <select
                  id="timeline"
                  name="timeline"
                  value={formData.timeline}
                  onChange={handleChange}
                  className="w-full h-11 px-3 bg-gray-50 border border-gray-300 rounded-md text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#011530]"
                >
                  <option value="">{t('modals.immigration.labels.timeline')}</option>
                  <option value="urgent">Urgent (1-3 months)</option>
                  <option value="short">Short-term (3-6 months)</option>
                  <option value="medium">Medium-term (6-12 months)</option>
                  <option value="long">Long-term (1+ years)</option>
                  <option value="flexible">Flexible</option>
                </select>
              </div>
            </div>

            {/* Additional information textarea */}
            <div>
              <Label htmlFor="additionalInfo" className="text-gray-700 mb-2 block font-medium">
                {t('modals.immigration.labels.additionalInfo')}
              </Label>
              <textarea
                id="additionalInfo"
                name="additionalInfo"
                value={formData.additionalInfo}
                onChange={handleChange}
                placeholder={t('modals.immigration.placeholders.additionalInfo') || ' '}
                rows="4"
                className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#011530] resize-none"
              />
            </div>
          </div>

          {/* Information note */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              <strong>Confidential Consultation:</strong> All information shared will be kept strictly confidential. Our immigration experts will assess your case and provide personalized guidance within 24-48 hours.
            </p>
          </div>

          {/* Submit button with loading state */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              onClick={onClose}
              variant="outline"
              className="px-6 h-11"
              disabled={isSubmitting}
            >
              {t('modals.immigration.buttons.cancel')}
            </Button>
            <Button
              type="submit"
              className="bg-[#011530] hover:bg-[#000d1f] text-white px-8 h-11 min-w-[120px]"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {t('modals.immigration.buttons.sending')}
                </>
              ) : (
                t('modals.immigration.buttons.send')
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ImmigrationModal;

