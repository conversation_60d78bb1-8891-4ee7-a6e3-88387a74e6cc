import React from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Calendar, User, ArrowRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';

const Blog = () => {
  const { t } = useTranslation();
  const blogPosts = t('blog.posts', { returnObjects: true });
  /*
    {
      category: 'Aviation',
      title: 'The Future of Private Jet Travel: Trends to Watch in 2025',
      author: 'Iriba Materanya',
      date: 'October 3, 2025',
      excerpt: 'Explore the upcoming trends in private aviation, from sustainable fuels to enhanced in-flight connectivity and personalized experiences.',
      imageText: 'Sleek private jet flying over a futuristic city skyline',
    },
    {
      category: 'Immigration',
      title: 'Navigating US Immigration: A Guide for Entrepreneurs',
      author: 'T <PERSON>',
      date: 'September 28, 2025',
      excerpt: 'A comprehensive overview of visa options and strategies for international entrepreneurs looking to establish their business in the United States.',
      imageText: 'Diverse group of entrepreneurs collaborating in a modern workspace',
    },
    {
      category: 'Travel',
      title: 'Top 5 Luxury Travel Destinations in Africa',
      author: '<PERSON>',
      date: 'September 15, 2025',
      excerpt: 'Discover breathtaking landscapes and unparalleled luxury with our curated list of the top travel destinations across the African continent.',
      imageText: 'Stunning aerial view of a luxury safari lodge in the Serengeti',
    },
    {
      category: 'Technology',
      title: 'How AI is Revolutionizing the Travel Booking Experience',
      author: 'Ushindi Matabaro Gabriel',
      date: 'September 5, 2025',
      excerpt: 'Learn how artificial intelligence is personalizing travel recommendations, streamlining bookings, and creating seamless journeys for travelers.',
      imageText: 'Abstract digital art representing artificial intelligence and global travel',
    },
  ];*/

  return (
    <>
      <Helmet>
        <title>Blog & Insights - Aviation, Travel, and Immigration News | Sparkmars</title>
        <meta name="description" content="Stay updated with the latest news, trends, and insights from the aviation, travel, and immigration industries on the Sparkmars blog." />
      </Helmet>

      {/* Main container with responsive padding */}
      <div className="pt-14 sm:pt-16 md:pt-20 bg-gradient-to-b from-white to-gray-50 text-gray-800">
        {/* Hero section with responsive spacing */}
        <section className="relative py-12 sm:py-16 md:py-20 overflow-hidden">
          {/* Background image */}
          <div className="absolute inset-0 z-0">
            <img className="w-full h-full object-cover" alt="Person writing in a journal with a laptop and coffee" src="https://images.unsplash.com/photo-1644412448740-40e5b6ded2dc" />
            <div className="absolute inset-0 bg-white/80 backdrop-blur-sm"></div>
          </div>

          {/* Hero content with responsive padding */}
          <div className="relative z-10 max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              {/* Responsive heading */}
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 px-2">
                <span className="gradient-text">{t('blog.hero.title')}</span>
              </h1>
              {/* Responsive subheading */}
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-3xl mx-auto px-4">
                {t('blog.hero.subtitle')}
              </p>
            </motion.div>
          </div>
        </section>

        {/* Blog posts section with responsive grid */}
        <section className="py-12 sm:py-16 md:py-20 bg-white">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            {/* Blog grid - responsive columns */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 sm:gap-6 md:gap-8">
              {blogPosts.map((post, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white rounded-xl sm:rounded-2xl overflow-hidden card-lift border border-gray-200 flex flex-col"
                >
                  {/* Post image with responsive height */}
                  <div className="relative h-48 sm:h-52 md:h-56 overflow-hidden">
                    <img className="w-full h-full object-cover" alt={post.title} src="https://images.unsplash.com/photo-1595872018818-97555653a011" />
                  </div>

                  {/* Post content with responsive padding */}
                  <div className="p-5 sm:p-6 flex flex-col flex-grow">
                    {/* Category badge */}
                    <p className="text-xs sm:text-sm font-semibold text-[#11a7ee] mb-2">{post.category}</p>

                    {/* Post title with responsive sizing */}
                    <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3 text-gray-900 flex-grow leading-tight">{post.title}</h3>

                    {/* Post excerpt with responsive text */}
                    <p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4 leading-relaxed">{post.excerpt}</p>

                    {/* Post metadata with responsive layout */}
                    <div className="text-xs sm:text-sm text-gray-500 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 mt-auto">
                      <div className="flex items-center space-x-2">
                        <User className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                        <span className="truncate">{post.author}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                        <span>{post.date}</span>
                      </div>
                    </div>

                    {/* Read more button */}
                    <Button variant="link" className="text-[#11a7ee] p-0 mt-3 sm:mt-4 justify-start h-auto text-sm sm:text-base">
                      {t('blog.readMore')} <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 ml-1" />
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Blog;