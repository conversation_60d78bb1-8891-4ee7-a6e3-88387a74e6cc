import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Plane, Car, FileText, Globe2, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link, useLocation } from 'react-router-dom';
import CarRentalModal from './CarRentalModal';
import VisaConsultationModal from './VisaConsultationModal';
import ImmigrationModal from './ImmigrationModal';

const ServiceDetails = () => {
  const location = useLocation();

  // State for managing modal visibility
  const [isCarRentalModalOpen, setIsCarRentalModalOpen] = useState(false);
  const [isVisaModalOpen, setIsVisaModalOpen] = useState(false);
  const [isImmigrationModalOpen, setIsImmigrationModalOpen] = useState(false);

  /**
   * Check URL hash on mount and when location changes to auto-open modals
   */
  useEffect(() => {
    const hash = location.hash.replace('#', '');

    if (hash === 'car-rental') {
      setIsCarRentalModalOpen(true);
    } else if (hash === 'visa') {
      setIsVisaModalOpen(true);
    } else if (hash === 'immigration') {
      setIsImmigrationModalOpen(true);
    }
  }, [location]);

  /**
   * Handle opening the appropriate modal based on service type
   */
  const handleInquireClick = (serviceTitle) => {
    if (serviceTitle === 'Car Rental') {
      setIsCarRentalModalOpen(true);
    } else if (serviceTitle === 'Visa Consultation') {
      setIsVisaModalOpen(true);
    } else if (serviceTitle === 'Immigration Services') {
      setIsImmigrationModalOpen(true);
    }
    // Aircraft Rental still goes to contact page (no modal needed)
  };

  const services = [
    {
      icon: Plane,
      title: 'Aircraft Rental',
      description: 'Access a global fleet of private jets for business or leisure. Our premium aircraft rental service offers unparalleled luxury, flexibility, and privacy. Fly on your schedule, to your destination, with world-class amenities.',
      features: ['24/7 Concierge Service', 'Global Fleet Access', 'Custom In-Flight Catering', 'Complete Privacy & Security'],
      imageText: 'Luxurious interior of a private jet with leather seats',
      color: '#19a6ec',
      useContactPage: true, // Aircraft rental uses contact page
    },
    {
      icon: Car,
      title: 'Car Rental',
      description: 'Your one-stop solution for ground transportation. We arrange everything from luxury sedans to spacious SUVs. Let us handle the logistics while you focus on your journey.',
      features: ['Wide Range of Vehicles', 'Airport & City Transfers', 'Chauffeur Services Available', 'Competitive Pricing'],
      imageText: 'Happy family checking into a luxury hotel lobby',
      color: '#273272',
      useContactPage: false, // Car rental uses modal
    },
    {
      icon: FileText,
      title: 'Visa Consultation',
      description: 'Navigating visa applications can be complex. Our expert consultants provide clear, accurate guidance and hands-on assistance to ensure your application is complete, correct, and submitted on time.',
      features: ['Personalized Document Checklist', 'Application Review & Filing', 'Interview Preparation', 'Status Tracking & Updates'],
      imageText: 'Close-up of a passport with visa stamps',
      color: '#14597d',
      useContactPage: false, // Visa consultation uses modal
    },
    {
      icon: Globe2,
      title: 'Immigration Services',
      description: 'Whether for work, family, or investment, our immigration experts guide you through every step of the process. We provide strategic advice and support for a smooth transition to your new home.',
      features: ['Residency & Citizenship Guidance', 'Business Immigration Strategy', 'Family Sponsorship Applications', 'Legal Partner Network'],
      imageText: 'Silhouette of a family looking at a new city skyline at sunset',
      color: '#011530',
      useContactPage: false, // Immigration services uses modal
    },
  ];

  return (
    <section className="py-12 sm:py-16 md:py-20 bg-white">
      {/* Container with responsive padding */}
      <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
        {/* Section header with responsive typography */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-10 sm:mb-12 md:mb-16"
        >
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 px-2">
            <span className="gradient-text">Detailed Service Offerings</span>
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4">
            Discover how our specialized services can help you achieve your global ambitions.
          </p>
        </motion.div>

        {/* Service items with responsive spacing */}
        <div className="space-y-12 sm:space-y-16 md:space-y-20">
          {services.map((service, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className={`grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12 items-center ${index % 2 !== 0 ? 'lg:grid-flow-col-dense lg:grid-cols-2 lg:[&>*:last-child]:col-start-1' : ''}`}
            >
              {/* Service image with responsive sizing */}
              <div className={`relative ${index % 2 !== 0 ? 'order-2 lg:order-1' : 'order-1'}`}>
                <img
                  className="rounded-xl sm:rounded-2xl shadow-2xl w-full h-64 sm:h-72 md:h-80 lg:h-96 object-cover"
                  alt={service.title}
                  src="https://images.unsplash.com/photo-1594024687935-aeb653eb0012"
                />
              </div>

              {/* Service content with responsive text */}
              <div className={`${index % 2 !== 0 ? 'order-1 lg:order-2' : 'order-2'}`}>
                {/* Service header with icon and title */}
                <div className="flex items-center space-x-3 sm:space-x-4 mb-4 sm:mb-5">
                  <div
                    className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center flex-shrink-0"
                    style={{ backgroundColor: `${service.color}20` }}
                  >
                    <service.icon className="w-5 h-5 sm:w-6 sm:h-6" style={{ color: service.color }} />
                  </div>
                  <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">{service.title}</h3>
                </div>

                {/* Service description with responsive text */}
                <p className="text-sm sm:text-base md:text-lg text-gray-600 mb-5 sm:mb-6 leading-relaxed">
                  {service.description}
                </p>

                {/* Feature list with responsive spacing */}
                <div className="space-y-2 sm:space-y-3 mb-6 sm:mb-8">
                  {service.features.map((feature, fIndex) => (
                    <div key={fIndex} className="flex items-start sm:items-center space-x-2 sm:space-x-3">
                      <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 flex-shrink-0 mt-0.5 sm:mt-0" />
                      <span className="text-sm sm:text-base text-gray-700 leading-relaxed">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* CTA button with responsive sizing - conditional rendering based on service type */}
                {service.useContactPage ? (
                  <Link to="/contact">
                    <Button
                      size="lg"
                      className="bg-[#19a6ec] hover:bg-[#1595d4] text-white font-semibold text-base sm:text-lg rounded-full shadow-lg hover:shadow-xl transition-all touch-manipulation w-full sm:w-auto"
                    >
                      Inquire Now
                    </Button>
                  </Link>
                ) : (
                  <Button
                    size="lg"
                    onClick={() => handleInquireClick(service.title)}
                    className="bg-[#19a6ec] hover:bg-[#1595d4] text-white font-semibold text-base sm:text-lg rounded-full shadow-lg hover:shadow-xl transition-all touch-manipulation w-full sm:w-auto"
                  >
                    Inquire Now
                  </Button>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Render modals */}
      <CarRentalModal
        isOpen={isCarRentalModalOpen}
        onClose={() => setIsCarRentalModalOpen(false)}
      />
      <VisaConsultationModal
        isOpen={isVisaModalOpen}
        onClose={() => setIsVisaModalOpen(false)}
      />
      <ImmigrationModal
        isOpen={isImmigrationModalOpen}
        onClose={() => setIsImmigrationModalOpen(false)}
      />
    </section>
  );
};

export default ServiceDetails;