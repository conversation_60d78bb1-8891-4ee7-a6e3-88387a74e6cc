import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Plane, Car, FileText, Globe2, ArrowRight, CheckCircle, Calendar, User, ArrowLeftRight, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { initEmailJS, sendFlightBookingEmail } from '@/services/emailService';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import '@/styles/datepicker-custom.css';
import { useTranslation } from 'react-i18next';


const Home = () => {
  // Initialize EmailJS on component mount
  useEffect(() => {
    initEmailJS();
  }, []);

  const { t } = useTranslation();

  // State for trip type and form data
  const [tripType, setTripType] = useState('one-way');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [flightFormData, setFlightFormData] = useState({
    from: '',
    to: '',
    departureDate: null, // Changed to null for DatePicker
    returnDate: null, // Changed to null for DatePicker
    passengers: '2',
  });

  const services = [
    {
      icon: Plane,
      title: t('home.services.aircraftRental.title'),
      description: t('home.services.aircraftRental.description'),
      color: '#11a7ed',
    },
    {
      icon: Car,
      title: t('home.services.flightTravel.title'),
      description: t('home.services.flightTravel.description'),
      color: '#11a7ed',
    },
    {
      icon: FileText,
      title: t('home.services.visaConsultant.title'),
      description: t('home.services.visaConsultant.description'),
      color: '#11a7ed',
    },
    {
      icon: Globe2,
      title: t('home.services.immigrationConsultant.title'),
      description: t('home.services.immigrationConsultant.description'),
      color: '#11a7ed',
    },
  ];

  /**
   * Handle input changes for flight booking form
   */
  const handleFlightFormChange = (e) => {
    const { name, value } = e.target;
    setFlightFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  /**
   * Validate flight booking form
   */
  const validateFlightForm = () => {
    if (!flightFormData.from.trim()) {
      toast({
        title: 'Departure location is required',
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!flightFormData.to.trim()) {
      toast({
        title: 'Destination is required',
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!flightFormData.departureDate) {
      toast({
        title: 'Departure date is required',
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (tripType === 'return' && !flightFormData.returnDate) {
      toast({
        title: 'Return date is required for return flights',
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    return true;
  };

  /**
   * Handle flight booking form submission
   */
  const handleSearch = async (e) => {
    e.preventDefault();

    // Validate form
    if (!validateFlightForm()) {
      return;
    }

    // Set loading state
    setIsSubmitting(true);

    try {
      // Prepare data for email - format dates for email
      const emailData = {
        from: flightFormData.from,
        to: flightFormData.to,
        departureDate: flightFormData.departureDate ? flightFormData.departureDate.toLocaleDateString('en-US', { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' }) : '',
        returnDate: tripType === 'return' && flightFormData.returnDate ? flightFormData.returnDate.toLocaleDateString('en-US', { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' }) : null,
        tripType: tripType === 'one-way' ? 'One-way' : 'Return',
        passengers: flightFormData.passengers,
      };

      // Send email via EmailJS
      await sendFlightBookingEmail(emailData);

      // Show success message
      toast({
        title: 'Flight Inquiry Sent Successfully! ✈️',
        description: 'Our team will contact you shortly with available options and pricing.',
        duration: 5000,
      });

      // Reset form
      setFlightFormData({
        from: '',
        to: '',
        departureDate: null,
        returnDate: null,
        passengers: '2',
      });
    } catch (error) {
      // Show error message
      toast({
        title: 'Failed to Send Inquiry',
        description: 'Please try again or contact us <NAME_EMAIL>',
        variant: 'destructive',
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>Sparkmars - Building a Generation Wealth | Aviation & Travel Services</title>
        <meta name="description" content="Sparkmars offers premium aircraft rental, flight booking, visa consultation, and immigration services. Building a generation wealth through global travel solutions." />
      </Helmet>

      <div className="relative min-h-screen bg-gradient-to-b from-white to-gray-50 text-gray-800">
        {/* Hero section with responsive height and padding */}
        <section className="relative min-h-[600px] sm:min-h-[650px] md:h-[70vh] flex items-center justify-center overflow-hidden pt-20 sm:pt-24 md:pt-20 pb-8 sm:pb-12">
          {/* Background image with proper mobile scaling */}
          <div className="absolute inset-0 z-0">
            <img
              className="w-full h-full object-cover object-center"
              alt="Modern private jet flying above clouds at sunset"
             src="https://images.unsplash.com/photo-1686549043259-3b8187675c06" />
            <div className="absolute inset-0 bg-black/40 sm:bg-black/30"></div>
          </div>

          {/* Hero content with responsive padding and spacing */}
          <div className="relative z-10 w-full max-w-5xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 text-white">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              {/* Responsive heading sizes for mobile readability */}
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-3 sm:mb-4 text-center md:text-left leading-tight">
                {t('home.hero.title')}
              </h1>
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl mb-6 sm:mb-8 text-center md:text-left">
                {t('home.hero.subtitle')}
              </p>

              {/* Booking form with mobile-first responsive design */}
              <div className="bg-white p-3 sm:p-4 md:p-6 rounded-lg shadow-2xl">
                {/* Radio group with touch-friendly spacing */}
                <RadioGroup defaultValue="one-way" className="flex flex-wrap gap-4 sm:gap-6 mb-4 sm:mb-6" onValueChange={setTripType}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="one-way" id="one-way" className="text-[#19a6ec] border-gray-400" />
                    <Label htmlFor="one-way" className="text-gray-800 text-sm sm:text-base cursor-pointer">{t('home.flightBooking.oneWay')}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="return" id="return" className="text-[#19a6ec] border-gray-400" />
                    <Label htmlFor="return" className="text-gray-800 text-sm sm:text-base cursor-pointer">{t('home.flightBooking.roundTrip')}</Label>
                  </div>
                </RadioGroup>

                {/* Form with mobile-stacked layout */}
                <form onSubmit={handleSearch} className="space-y-0">
                  <div className="bg-[#11a7ed] p-0.5 sm:p-1 rounded-lg">
                    {/* Mobile: stack vertically, Tablet+: grid layout */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-12 gap-0 sm:gap-px bg-white rounded-md">
                      {/* From field - full width on mobile */}
                      <div className="relative sm:col-span-1 md:col-span-3">
                        <Input
                          type="text"
                          name="from"
                          value={flightFormData.from}
                          onChange={handleFlightFormChange}
                          placeholder={t('home.flightBooking.fromPlaceholder')}
                          className="w-full h-12 sm:h-14 pl-4 pr-10 border-0 rounded-t-md sm:rounded-tl-md sm:rounded-tr-none md:rounded-l-md md:rounded-tr-none focus:ring-0 text-gray-800 text-sm sm:text-base"
                          required
                        />
                        <ArrowLeftRight className="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                      </div>

                      {/* To field */}
                      <div className="relative sm:col-span-1 md:col-span-3">
                        <Input
                          type="text"
                          name="to"
                          value={flightFormData.to}
                          onChange={handleFlightFormChange}
                          placeholder={t('home.flightBooking.toPlaceholder')}
                          className="w-full h-12 sm:h-14 pl-4 border-0 sm:rounded-tr-md md:rounded-none focus:ring-0 text-gray-800 text-sm sm:text-base"
                          required
                        />
                      </div>

                      {/* Departure date with DatePicker */}
                      <div className="relative sm:col-span-1 md:col-span-2">
                        <DatePicker
                          selected={flightFormData.departureDate}
                          onChange={(date) => setFlightFormData(prev => ({ ...prev, departureDate: date }))}
                          minDate={new Date()}
                          dateFormat="EEE, MMM d, yyyy"
                          placeholderText={t('home.flightBooking.departureDate')}
                          className="w-full h-12 sm:h-14 pl-10 border-0 sm:rounded-bl-md md:rounded-none focus:ring-0 text-gray-800 text-sm sm:text-base bg-white"
                          wrapperClassName="w-full"
                          required
                        />
                        <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400 pointer-events-none" />
                      </div>

                      {/* Return date with DatePicker - conditional rendering */}
                      {tripType === 'return' && (
                        <div className="relative sm:col-span-1 md:col-span-2">
                          <DatePicker
                            selected={flightFormData.returnDate}
                            onChange={(date) => setFlightFormData(prev => ({ ...prev, returnDate: date }))}
                            minDate={flightFormData.departureDate || new Date()}
                            dateFormat="EEE, MMM d, yyyy"
                            placeholderText={t('home.flightBooking.returnDate')}
                            className="w-full h-12 sm:h-14 pl-10 border-0 sm:rounded-br-md md:rounded-none focus:ring-0 text-gray-800 text-sm sm:text-base bg-white"
                            wrapperClassName="w-full"
                            required={tripType === 'return'}
                          />
                          <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400 pointer-events-none" />
                        </div>
                      )}

                      {/* Passengers count */}
                      <div className={`relative ${tripType === 'return' ? 'sm:col-span-1' : 'sm:col-span-1'} md:col-span-1`}>
                        <Input
                          type="number"
                          name="passengers"
                          value={flightFormData.passengers}
                          onChange={handleFlightFormChange}
                          min="1"
                          max="20"
                          className="w-full h-12 sm:h-14 pl-10 border-0 focus:ring-0 text-gray-800 text-sm sm:text-base"
                        />
                        <User className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                      </div>

                      {/* Inquire button - touch-friendly size with loading state */}
                      <div className={`${tripType === 'return' ? 'sm:col-span-1' : 'sm:col-span-1'} md:col-span-1`}>
                        <Button
                          type="submit"
                          disabled={isSubmitting}
                          className="w-full h-12 sm:h-14 bg-[#19a6ec] hover:bg-[#1595d4] text-white font-bold text-base sm:text-lg rounded-b-md sm:rounded-br-md sm:rounded-bl-none md:rounded-r-md md:rounded-bl-none touch-manipulation disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              Sending...
                            </>
                          ) : (
                            <span>{t('home.flightBooking.searchFlights')}</span>
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Services section with responsive spacing */}
        <section className="py-12 sm:py-16 md:py-20 bg-white">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            {/* Section header with responsive typography */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="text-center mb-8 sm:mb-12 md:mb-16"
            >
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 px-2">
                <span className="gradient-text">{t('home.services.title')}</span>
              </h2>
              <p className="text-base sm:text-lg md:text-xl text-gray-600 max-w-2xl mx-auto px-4">
                {t('home.services.subtitle')}
              </p>
            </motion.div>

            {/* Service cards grid - responsive columns */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 md:gap-8">
              {services.map((service, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 card-lift cursor-pointer border border-gray-200 transition-all hover:shadow-2xl hover:-translate-y-1"
                >
                  {/* Icon with responsive sizing */}
                  <div
                    className="w-14 h-14 sm:w-16 sm:h-16 rounded-full flex items-center justify-center mb-4 sm:mb-6"
                    style={{ backgroundColor: `${service.color}20` }}
                  >
                    <service.icon className="w-7 h-7 sm:w-8 sm:h-8" style={{ color: service.color }} />
                  </div>
                  {/* Service title and description with responsive text */}
                  <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3 text-gray-900">{service.title}</h3>
                  <p className="text-sm sm:text-base text-gray-600 leading-relaxed">{service.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose section with responsive layout */}
        <section className="py-12 sm:py-16 md:py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            {/* Two-column layout: stack on mobile, side-by-side on desktop */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-10 lg:gap-12 items-center">
              {/* Text content */}
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
                className="order-2 lg:order-1"
              >
                {/* Responsive heading */}
                <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6">
                  <span className="gradient-text">{t('home.whyChoose.title')}</span>
                </h2>
                {/* Responsive paragraph */}
                <p className="text-base sm:text-lg md:text-xl text-gray-600 mb-6 sm:mb-8 leading-relaxed">
                  A multi-talented business built with a culture-oriented vision, dedicated to providing exceptional service across continents.
                </p>
                {/* Feature list with responsive spacing */}
                <div className="space-y-3 sm:space-y-4">
                  {[
                    t('home.whyChoose.expertise.description'),
                    t('home.whyChoose.support.description'),
                    t('home.whyChoose.global.description'),
                    t('home.whyChoose.trusted.description'),
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="flex items-start sm:items-center space-x-3"
                    >
                      <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-[#11a7ed] flex-shrink-0 mt-0.5 sm:mt-0" />
                      <span className="text-sm sm:text-base text-gray-700 leading-relaxed">{item}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Image - show first on mobile for visual hierarchy */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
                className="relative order-1 lg:order-2"
              >
                <img
                  className="rounded-xl sm:rounded-2xl shadow-2xl w-full h-auto object-cover"
                  alt="Professional business team collaborating in modern office"
                 src="https://images.unsplash.com/photo-1629904888132-038af9df34ab" />
              </motion.div>
            </div>
          </div>
        </section>

        {/* Locations section with responsive cards */}
        <section className="py-12 sm:py-16 md:py-20 bg-white">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            {/* Section header */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="text-center mb-8 sm:mb-12 md:mb-16"
            >
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4">
                <span className="gradient-text">{t('home.locations.title')}</span>
              </h2>
              <p className="text-base sm:text-lg md:text-xl text-gray-600">{t('home.locations.subtitle')}</p>
            </motion.div>

            {/* Location cards - stack on mobile, side-by-side on tablet+ */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
              {/* Goma office */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="bg-white rounded-xl sm:rounded-2xl p-5 sm:p-6 md:p-8 border border-gray-200 shadow-sm"
              >
                <h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4 text-[#19a6ec]">Goma, D.R. Congo</h3>
                <p className="text-sm sm:text-base text-gray-700 mb-3 sm:mb-4">24. Avenue Boulevard Kanyamuhanga, Bâtiment JP Bishweka</p>
                <p className="text-sm sm:text-base text-gray-600 mb-4">Phone: +243 995 183 556</p>
                {/* Responsive map height */}
                <div className="w-full h-48 sm:h-56 md:h-64 bg-gray-200 rounded-lg overflow-hidden">
                  <iframe
                    src="https://www.openstreetmap.org/export/embed.html?bbox=29.2200%2C-1.6800%2C29.2400%2C-1.6600&layer=mapnik&marker=-1.6700%2C29.2300"
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    title="Goma Office Location"
                  ></iframe>
                </div>
              </motion.div>

              {/* Houston office */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="bg-white rounded-xl sm:rounded-2xl p-5 sm:p-6 md:p-8 border border-gray-200 shadow-sm"
              >
                <h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4 text-[#19a6ec]">Houston, USA</h3>
                <p className="text-sm sm:text-base text-gray-700 mb-3 sm:mb-4">6671 Southwest Freeway, Houston, TX 77074</p>
                <p className="text-sm sm:text-base text-gray-600 mb-4">Phone: +****************</p>
                {/* Responsive map height */}
                <div className="w-full h-48 sm:h-56 md:h-64 bg-gray-200 rounded-lg overflow-hidden">
                  <iframe
                    src="https://www.openstreetmap.org/export/embed.html?bbox=-95.4900%2C29.6900%2C-95.4700%2C29.7100&layer=mapnik&marker=29.7000%2C-95.4800"
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    title="Houston Office Location"
                  ></iframe>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* CTA section with responsive design */}
        <section className="py-12 sm:py-16 md:py-20 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              {/* Responsive heading */}
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 px-2">
                <span className="gradient-text">{t('home.cta.title')}</span>
              </h2>
              {/* Responsive paragraph */}
              <p className="text-base sm:text-lg md:text-xl text-gray-600 mb-6 sm:mb-8 px-4">
                {t('home.cta.subtitle')}
              </p>
              {/* Touch-friendly button with responsive sizing */}
              <Link to="/contact">
                <Button
                  size="lg"
                  className="bg-[#19a6ec] hover:bg-[#1595d4] text-white px-8 sm:px-10 md:px-12 py-4 sm:py-5 md:py-6 text-base sm:text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all touch-manipulation"
                >
                  {t('home.cta.button')} <ArrowRight className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
                </Button>
              </Link>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Home;