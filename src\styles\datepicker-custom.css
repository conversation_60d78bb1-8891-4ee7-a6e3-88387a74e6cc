/* Custom styles for react-datepicker to match Sparkmars design */

/* DatePicker input styling */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container {
  width: 100%;
}

.react-datepicker__input-container input {
  width: 100%;
  cursor: pointer;
}

/* Calendar container */
.react-datepicker {
  font-family: inherit;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Header styling */
.react-datepicker__header {
  background-color: #19a6ec;
  border-bottom: none;
  border-radius: 0.5rem 0.5rem 0 0;
  padding-top: 0.75rem;
}

.react-datepicker__current-month {
  color: white;
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.react-datepicker__day-name {
  color: white;
  font-weight: 500;
  width: 2.5rem;
  line-height: 2.5rem;
  margin: 0.166rem;
}

/* Navigation arrows */
.react-datepicker__navigation {
  top: 0.75rem;
}

.react-datepicker__navigation-icon::before {
  border-color: white;
  border-width: 2px 2px 0 0;
}

.react-datepicker__navigation:hover *::before {
  border-color: rgba(255, 255, 255, 0.8);
}

/* Day cells */
.react-datepicker__day {
  color: #374151;
  width: 2.5rem;
  line-height: 2.5rem;
  margin: 0.166rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.react-datepicker__day:hover {
  background-color: #e0f2fe;
  color: #0369a1;
}

.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected {
  background-color: #19a6ec;
  color: white;
  font-weight: 600;
}

.react-datepicker__day--selected:hover,
.react-datepicker__day--keyboard-selected:hover {
  background-color: #1589c9;
}

.react-datepicker__day--disabled {
  color: #d1d5db;
  cursor: not-allowed;
}

.react-datepicker__day--disabled:hover {
  background-color: transparent;
  color: #d1d5db;
}

.react-datepicker__day--today {
  font-weight: 600;
  border: 2px solid #19a6ec;
}

/* Month container */
.react-datepicker__month {
  margin: 0.75rem;
}

/* Responsive adjustments for mobile */
@media (max-width: 640px) {
  .react-datepicker {
    font-size: 0.875rem;
  }

  .react-datepicker__day-name,
  .react-datepicker__day {
    width: 2rem;
    line-height: 2rem;
    margin: 0.125rem;
  }

  .react-datepicker__current-month {
    font-size: 0.875rem;
  }
}

/* Ensure calendar appears above other elements */
.react-datepicker-popper {
  z-index: 9999 !important;
}

/* Triangle pointer styling */
.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle {
  fill: #19a6ec;
  color: #19a6ec;
}

.react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle {
  fill: #19a6ec;
  color: #19a6ec;
}

