import React from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { Linkedin, Mail } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const Team = () => {
  const { t } = useTranslation();

  const teamMembers = [
    {
      key: 'iriba',
      name: t('team.members.iriba.name'),
      title: t('team.members.iriba.title'),
      bio: t('team.members.iriba.bio'),
      color: '#11a7ed',
      image: '/team/iriba_materanya.svg',
    },
    {
      key: 'leon',
      name: t('team.members.leon.name'),
      title: t('team.members.leon.title'),
      bio: t('team.members.leon.bio'),
      color: '#11a7ed',
      image: '/team/t_leon_preston.svg',
    },
    {
      key: 'ushindi',
      name: t('team.members.ushindi.name'),
      title: t('team.members.ushindi.title'),
      bio: t('team.members.ushindi.bio'),
      color: '#11a7ed',
      image: '/team/ushindi_matabaro_gabriel.svg',
    },
    {
      key: 'kimberly',
      name: t('team.members.kimberly.name'),
      title: t('team.members.kimberly.title'),
      bio: t('team.members.kimberly.bio'),
      color: '#11a7ed',
      image: '/team/kimberly_broussard.svg',
    },
    {
      key: 'andrea',
      name: t('team.members.andrea.name'),
      title: t('team.members.andrea.title'),
      bio: t('team.members.andrea.bio'),
      color: '#11a7ed',
      image: '/team/andrea_paolo_minoti.svg',
    },
  ];

  return (
    <>
      <Helmet>
        <title>Our Team - Meet the Sparkmars Leadership | Expert Professionals</title>
        <meta name="description" content="Meet the expert team behind Sparkmars. Our leadership brings decades of combined experience in aviation, travel, and immigration services." />
      </Helmet>

      {/* Main container with responsive padding */}
      <div className="pt-14 sm:pt-16 md:pt-20 bg-gradient-to-b from-white to-gray-50 text-gray-800">
        {/* Hero section with responsive spacing */}
        <section className="relative py-12 sm:py-16 md:py-20 overflow-hidden">
          {/* Background image */}
          <div className="absolute inset-0 z-0">
            <img className="w-full h-full object-cover" alt="Professional business team in modern office" src="https://images.unsplash.com/photo-1651009188116-bb5f80eaf6aa" />
            <div className="absolute inset-0 bg-white/80 backdrop-blur-sm"></div>
          </div>

          {/* Hero content with responsive padding */}
          <div className="relative z-10 max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              {/* Responsive heading */}
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 px-2">
                <span className="gradient-text">{t('team.hero.title')}</span>
              </h1>
              {/* Responsive subheading */}
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-3xl mx-auto px-4">
                {t('team.hero.subtitle')}
              </p>
            </motion.div>
          </div>
        </section>

        {/* Team members section with responsive grid */}
        <section className="py-12 sm:py-16 md:py-20 bg-white">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            {/* Section header with responsive typography */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="text-center mb-10 sm:mb-12 md:mb-16"
            >
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 px-2">
                <span className="gradient-text">{t('team.leadership.title')}</span>
              </h2>
              <p className="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4 leading-relaxed">
                {t('team.leadership.subtitle')}
              </p>
            </motion.div>

            {/* Team grid - responsive columns */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 sm:gap-6 md:gap-8">
              {teamMembers.map((member, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white rounded-xl sm:rounded-2xl overflow-hidden card-lift border border-gray-200"
                >
                  {/* Member photo with responsive height */}
                  <div className="relative h-64 sm:h-72 md:h-80 overflow-hidden bg-gray-100">
                    <img
                      className="w-full h-full object-contain p-4"
                      alt={`${member.name} - ${member.title}`}
                      src={member.image} />
                  </div>

                  {/* Member info with responsive padding */}
                  <div className="p-5 sm:p-6 text-center">
                    {/* Member name with responsive sizing */}
                    <h3 className="text-xl sm:text-2xl font-bold mb-1 text-gray-900">{member.name}</h3>

                    {/* Member title with responsive sizing */}
                    <p className="text-sm sm:text-base mb-3 sm:mb-4 font-semibold" style={{ color: member.color }}>
                      {member.title}
                    </p>

                    {/* Member bio with responsive text */}
                    <p className="text-sm sm:text-base text-gray-600 mb-5 sm:mb-6 leading-relaxed">{member.bio}</p>

                    {/* Social links with touch-friendly spacing */}
                    <div className="flex space-x-4 justify-center">
                      <motion.a
                        whileHover={{ scale: 1.1 }}
                        href="#"
                        className="text-gray-500 hover:text-[#11a7ee] transition-colors p-2 -m-2 touch-manipulation"
                        aria-label={`${member.name} LinkedIn`}
                      >
                        <Linkedin className="w-5 h-5" />
                      </motion.a>
                      <motion.a
                        whileHover={{ scale: 1.1 }}
                        href="mailto:<EMAIL>"
                        className="text-gray-500 hover:text-[#11a7ee] transition-colors p-2 -m-2 touch-manipulation"
                        aria-label={`Email ${member.name}`}
                      >
                        <Mail className="w-5 h-5" />
                      </motion.a>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Join our team CTA section with responsive design */}
        <section className="py-12 sm:py-16 md:py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            {/* Two-column layout: stack on mobile, side-by-side on desktop */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-10 lg:gap-12 items-center">
              {/* Text content */}
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
                className="order-2 lg:order-1"
              >
                {/* Responsive heading */}
                <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6">
                  <span className="gradient-text">{t('team.join.title')}</span>
                </h2>
                {/* Responsive paragraphs */}
                <p className="text-base sm:text-lg md:text-xl text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                  {t('team.join.description1')}
                </p>
                <p className="text-base sm:text-lg md:text-xl text-gray-700 mb-6 sm:mb-8 leading-relaxed">
                  {t('team.join.description2')}
                </p>
                {/* CTA button with responsive sizing */}
                <a
                  href="mailto:<EMAIL>"
                  className="inline-block bg-[#11a7ee] hover:bg-[#0d8bc9] text-white px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-base sm:text-lg transition-all shadow-lg hover:shadow-xl touch-manipulation w-full sm:w-auto text-center"
                >
                  {t('team.join.cta')}
                </a>
              </motion.div>

              {/* Image with responsive sizing */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
                className="relative order-1 lg:order-2"
              >
                <img
                  className="rounded-xl sm:rounded-2xl shadow-2xl w-full h-64 sm:h-80 md:h-96 lg:h-auto object-cover"
                  alt="Team collaboration and innovation"
                  src="https://images.unsplash.com/photo-1637622124152-33adfabcc923" />
              </motion.div>
            </div>
          </div>
        </section>

        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                <span className="gradient-text">{t('team.culture.title')}</span>
              </h2>
              <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
                {t('team.culture.description')}
              </p>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Team;