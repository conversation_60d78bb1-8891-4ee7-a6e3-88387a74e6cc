import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Mail, Phone, MapPin, Facebook, Twitter, Linkedin, Instagram } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import logo from '/sparkmars_logo.svg';

const Footer = () => {
  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#7D7D7D] text-white pt-12 sm:pt-14 md:pt-16 pb-6 sm:pb-8 border-t border-white/20">
      {/* Footer container with responsive padding */}
      <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
        {/* Footer grid - responsive columns: 1 on mobile, 2 on tablet, 4 on desktop */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-10 lg:gap-12 mb-8 sm:mb-10 md:mb-12">
          {/* Brand and social links */}
          <div className="text-center sm:text-left">
            <img
              src={logo}
              alt="Sparkmars Logo"
              className="h-10 sm:h-12 w-auto mb-3 sm:mb-4 mx-auto sm:mx-0"
            />
            <p className="text-sm sm:text-base text-white/90 mb-4">{t('footer.tagline')}</p>
            {/* Social icons with touch-friendly spacing */}
            <div className="flex space-x-4 justify-center sm:justify-start">
              <motion.a
                whileHover={{ scale: 1.1 }}
                href="#"
                className="text-white hover:text-[#11a7ed] transition-colors p-2 -m-2 touch-manipulation"
                aria-label="Facebook"
              >
                <Facebook className="w-5 h-5" />
              </motion.a>
              <motion.a
                whileHover={{ scale: 1.1 }}
                href="#"
                className="text-white hover:text-[#11a7ed] transition-colors p-2 -m-2 touch-manipulation"
                aria-label="Twitter"
              >
                <Twitter className="w-5 h-5" />
              </motion.a>
              <motion.a
                whileHover={{ scale: 1.1 }}
                href="#"
                className="text-white hover:text-[#11a7ed] transition-colors p-2 -m-2 touch-manipulation"
                aria-label="LinkedIn"
              >
                <Linkedin className="w-5 h-5" />
              </motion.a>
              <motion.a
                whileHover={{ scale: 1.1 }}
                href="#"
                className="text-white hover:text-[#11a7ed] transition-colors p-2 -m-2 touch-manipulation"
                aria-label="Instagram"
              >
                <Instagram className="w-5 h-5" />
              </motion.a>
            </div>
          </div>

          {/* Quick links with responsive text */}
          <div className="text-center sm:text-left">
            <span className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 block text-white">{t('footer.quickLinks')}</span>
            <ul className="space-y-2">
              <li><Link to="/" className="text-sm sm:text-base text-white/90 hover:text-[#e5e4e4] hover:underline transition-colors inline-block py-1">{t('nav.home')}</Link></li>
              <li><Link to="/about" className="text-sm sm:text-base text-white/90 hover:text-[#e5e4e4] hover:underline transition-colors inline-block py-1">{t('nav.about')}</Link></li>
              <li><Link to="/services" className="text-sm sm:text-base text-white/90 hover:text-[#e5e4e4] hover:underline transition-colors inline-block py-1">{t('nav.services')}</Link></li>
              <li><Link to="/team" className="text-sm sm:text-base text-white/90 hover:text-[#e5e4e4] hover:underline transition-colors inline-block py-1">{t('nav.team')}</Link></li>
              <li><Link to="/blog" className="text-sm sm:text-base text-white/90 hover:text-[#e5e4e4] hover:underline transition-colors inline-block py-1">{t('nav.blog')}</Link></li>
              <li><Link to="/contact" className="text-sm sm:text-base text-white/90 hover:text-[#e5e4e4] hover:underline transition-colors inline-block py-1">{t('nav.contact')}</Link></li>
            </ul>
          </div>

          {/* Goma office info with responsive text */}
          <div className="text-center sm:text-left">
            <span className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 block text-white">Goma Office</span>
            <div className="space-y-2 sm:space-y-3 text-white/90 text-sm sm:text-base">
              <div className="flex items-start space-x-2 justify-center sm:justify-start">
                <MapPin className="w-4 h-4 sm:w-5 sm:h-5 text-[#11a7ed] flex-shrink-0 mt-1" />
                <p className="text-left">24. Avenue Boulevard Kanyamuhanga, Bâtiment JP Bishweka, Goma, D.R. Congo</p>
              </div>
              <div className="flex items-center space-x-2 justify-center sm:justify-start">
                <Phone className="w-4 h-4 sm:w-5 sm:h-5 text-[#11a7ed] flex-shrink-0" />
                <p>+243 995 183 556</p>
              </div>
            </div>
          </div>

          {/* Houston office info with responsive text */}
          <div className="text-center sm:text-left">
            <span className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 block text-white">Houston Office</span>
            <div className="space-y-2 sm:space-y-3 text-white/90 text-sm sm:text-base">
              <div className="flex items-start space-x-2 justify-center sm:justify-start">
                <MapPin className="w-4 h-4 sm:w-5 sm:h-5 text-[#11a7ed] flex-shrink-0 mt-1" />
                <p className="text-left">6671 Southwest Freeway, Houston, TX 77074, USA</p>
              </div>
              <div className="flex items-center space-x-2 justify-center sm:justify-start">
                <Phone className="w-4 h-4 sm:w-5 sm:h-5 text-[#11a7ed] flex-shrink-0" />
                <p>+****************</p>
              </div>
              <div className="flex items-center space-x-2 justify-center sm:justify-start">
                <Mail className="w-4 h-4 sm:w-5 sm:h-5 text-[#11a7ed] flex-shrink-0" />
                <p><EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright section with responsive text */}
        <div className="border-t border-white/20 pt-6 sm:pt-8 text-center text-white/80">
          <p className="text-xs sm:text-sm">&copy; {currentYear} Sparkmars. {t('footer.rights')}</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;