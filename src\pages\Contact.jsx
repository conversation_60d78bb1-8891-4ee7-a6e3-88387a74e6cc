import React, { useState } from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { Mail, Phone, MapPin, Send } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';

const Contact = () => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!formData.name || !formData.email || !formData.message) {
      toast({
        title: t('validation.required'),
        variant: "destructive",
        duration: 3000,
      });
      return;
    }

    toast({
      title: t('toast.languageNotImplemented'),
      duration: 3000,
    });
  };

  const contactInfo = [
    {
      icon: Mail,
      title: t('contact.cards.email.title'),
      details: '<EMAIL>',
      link: 'mailto:<EMAIL>',
      color: '#19a6ec',
    },
    {
      icon: Phone,
      title: t('contact.cards.callGoma.title'),
      details: '+243 995 183 556',
      link: 'tel:+243995183556',
      color: '#273272',
    },
    {
      icon: Phone,
      title: t('contact.cards.callHouston.title'),
      details: '+****************',
      link: 'tel:+18323152528',
      color: '#14597d',
    },
  ];

  return (
    <>
      <Helmet>
        <title>Contact Sparkmars - Get in Touch | Aviation & Travel Services</title>
        <meta name="description" content="Contact Sparkmars for aircraft rental, flight booking, visa consultation, and immigration services. Offices in Goma, DRC and Houston, USA." />
      </Helmet>

      {/* Main container with responsive padding */}
      <div className="pt-14 sm:pt-16 md:pt-20 bg-gradient-to-b from-white to-gray-50 text-gray-800">
        {/* Hero section with responsive spacing */}
        <section className="relative py-12 sm:py-16 md:py-20 overflow-hidden">
          {/* Background image */}
          <div className="absolute inset-0 z-0">
            <img
              className="w-full h-full object-cover"
              alt="Modern communication and global connectivity"
             src="https://images.unsplash.com/photo-1679414104055-7c34bd4787e4" />
            <div className="absolute inset-0 bg-white/80 backdrop-blur-sm"></div>
          </div>

          {/* Hero content with responsive padding */}
          <div className="relative z-10 max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              {/* Responsive heading */}
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 px-2">
                <span className="gradient-text">{t('contact.hero.title')}</span>
              </h1>
              {/* Responsive subheading */}
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-3xl mx-auto px-4">
                {t('contact.hero.subtitle')}
              </p>
            </motion.div>
          </div>
        </section>

        {/* Contact info and form section */}
        <section className="py-12 sm:py-16 md:py-20 bg-white">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            {/* Contact info cards - responsive grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6 md:gap-8 mb-8 sm:mb-12 md:mb-16">
              {contactInfo.map((info, index) => (
                <motion.a
                  key={index}
                  href={info.link}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 card-lift text-center border border-gray-200 touch-manipulation"
                >
                  {/* Icon with responsive sizing */}
                  <div
                    className="w-14 h-14 sm:w-16 sm:h-16 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4"
                    style={{ backgroundColor: `${info.color}20` }}
                  >
                    <info.icon className="w-7 h-7 sm:w-8 sm:h-8" style={{ color: info.color }} />
                  </div>
                  {/* Responsive text */}
                  <h3 className="text-lg sm:text-xl font-bold mb-2 text-gray-900">{info.title}</h3>
                  <p className="text-sm sm:text-base text-gray-600">{info.details}</p>
                </motion.a>
              ))}
            </div>

            {/* Form and office info - responsive two-column layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-10 lg:gap-12">
              {/* Contact form */}
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
                className="bg-white rounded-xl sm:rounded-2xl p-5 sm:p-6 md:p-8 border border-gray-200 shadow-lg"
              >
                {/* Form heading with responsive sizing */}
                <h2 className="text-2xl sm:text-3xl font-bold mb-5 sm:mb-6">
                  <span className="gradient-text">{t('contact.form.title')}</span>
                </h2>

                {/* Form with responsive spacing */}
                <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-5 md:space-y-6">
                  {/* Full Name field */}
                  <div>
                    <Label htmlFor="name" className="text-gray-700 mb-2 block font-semibold text-sm sm:text-base">
                      {t('contact.form.name')}
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder={t('contact.form.namePlaceholder')}
                      className="bg-gray-50 border-gray-300 text-gray-800 placeholder:text-gray-400 h-11 sm:h-12 text-sm sm:text-base"
                      required
                    />
                  </div>

                  {/* Email field */}
                  <div>
                    <Label htmlFor="email" className="text-gray-700 mb-2 block font-semibold text-sm sm:text-base">
                      {t('contact.form.email')}
                    </Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder={t('contact.form.emailPlaceholder')}
                      className="bg-gray-50 border-gray-300 text-gray-800 placeholder:text-gray-400 h-11 sm:h-12 text-sm sm:text-base"
                      required
                    />
                  </div>

                  {/* Phone field */}
                  <div>
                    <Label htmlFor="phone" className="text-gray-700 mb-2 block font-semibold text-sm sm:text-base">
                      {t('contact.form.phone')}
                    </Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder={t('contact.form.phonePlaceholder')}
                      className="bg-gray-50 border-gray-300 text-gray-800 placeholder:text-gray-400 h-11 sm:h-12 text-sm sm:text-base"
                    />
                  </div>

                  {/* Subject field */}
                  <div>
                    <Label htmlFor="subject" className="text-gray-700 mb-2 block font-semibold text-sm sm:text-base">
                      {t('contact.form.subject')}
                    </Label>
                    <Input
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      placeholder={t('contact.form.subjectPlaceholder')}
                      className="bg-gray-50 border-gray-300 text-gray-800 placeholder:text-gray-400 h-11 sm:h-12 text-sm sm:text-base"
                    />
                  </div>

                  {/* Message textarea */}
                  <div>
                    <Label htmlFor="message" className="text-gray-700 mb-2 block font-semibold text-sm sm:text-base">
                      {t('contact.form.message')}
                    </Label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      placeholder={t('contact.form.messagePlaceholder')}
                      rows={5}
                      className="bg-gray-50 border-gray-300 text-gray-800 placeholder:text-gray-400 text-sm sm:text-base resize-none"
                      required
                    />
                  </div>

                  {/* Submit button - touch-friendly sizing */}
                  <Button
                    type="submit"
                    size="lg"
                    className="w-full h-12 sm:h-14 bg-[#19a6ec] hover:bg-[#1595d4] text-white font-semibold text-base sm:text-lg rounded-full shadow-lg hover:shadow-xl transition-all touch-manipulation"
                  >
                    {t('contact.form.send')} <Send className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
                  </Button>
                </form>
              </motion.div>

              {/* Office locations with maps */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
                className="space-y-6 sm:space-y-8"
              >
                {/* Goma office card */}
                <div className="bg-white rounded-xl sm:rounded-2xl p-5 sm:p-6 md:p-8 border border-gray-200 shadow-lg">
                  <div className="flex items-start space-x-3 sm:space-x-4 mb-4">
                    <MapPin className="w-5 h-5 sm:w-6 sm:h-6 text-[#19a6ec] flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="text-lg sm:text-xl font-bold mb-2 text-gray-900">Goma Office</h3>
                      <p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4 leading-relaxed">
                        24. Avenue Boulevard Kanyamuhanga<br />
                        Bâtiment JP Bishweka<br />
                        Goma, D.R. Congo
                      </p>
                      <p className="text-sm sm:text-base text-gray-600">
                        <strong>Phone:</strong> +243 995 183 556
                      </p>
                    </div>
                  </div>
                  {/* Responsive map height */}
                  <div className="w-full h-48 sm:h-56 md:h-64 bg-gray-200 rounded-lg overflow-hidden mt-4">
                    <iframe
                      src="https://www.openstreetmap.org/export/embed.html?bbox=29.2200%2C-1.6800%2C29.2400%2C-1.6600&layer=mapnik&marker=-1.6700%2C29.2300"
                      width="100%"
                      height="100%"
                      style={{ border: 0 }}
                      title="Goma Office Location"
                    ></iframe>
                  </div>
                </div>

                {/* Houston office card */}
                <div className="bg-white rounded-xl sm:rounded-2xl p-5 sm:p-6 md:p-8 border border-gray-200 shadow-lg">
                  <div className="flex items-start space-x-3 sm:space-x-4 mb-4">
                    <MapPin className="w-5 h-5 sm:w-6 sm:h-6 text-[#19a6ec] flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="text-lg sm:text-xl font-bold mb-2 text-gray-900">Houston Office</h3>
                      <p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4 leading-relaxed">
                        6671 Southwest Freeway<br />
                        Houston, TX 77074<br />
                        United States
                      </p>
                      <p className="text-sm sm:text-base text-gray-600">
                        <strong>Phone:</strong> +****************
                      </p>
                    </div>
                  </div>
                  {/* Responsive map height */}
                  <div className="w-full h-48 sm:h-56 md:h-64 bg-gray-200 rounded-lg overflow-hidden mt-4">
                    <iframe
                      src="https://www.openstreetmap.org/export/embed.html?bbox=-95.4900%2C29.6900%2C-95.4700%2C29.7100&layer=mapnik&marker=29.7000%2C-95.4800"
                      width="100%"
                      height="100%"
                      style={{ border: 0 }}
                      title="Houston Office Location"
                    ></iframe>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Business hours section with responsive design */}
        <section className="py-12 sm:py-16 md:py-20 bg-gray-50">
          <div className="max-w-4xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              {/* Responsive heading */}
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-5 sm:mb-6 px-2">
                <span className="gradient-text">{t('contact.hours.title')}</span>
              </h2>
              {/* Business hours card with responsive padding */}
              <div className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 inline-block border border-gray-200 shadow-lg max-w-full">
                <p className="text-base sm:text-lg md:text-xl text-gray-700 mb-3 sm:mb-4">
                  <strong>{t('contact.hours.weekdays').split(':')[0]}:</strong> {t('contact.hours.weekdays').split(':')[1]?.trim()}
                </p>
                <p className="text-base sm:text-lg md:text-xl text-gray-700 mb-3 sm:mb-4">
                  <strong>{t('contact.hours.saturday').split(':')[0]}:</strong> {t('contact.hours.saturday').split(':')[1]?.trim()}
                </p>
                <p className="text-base sm:text-lg md:text-xl text-gray-700">
                  <strong>{t('contact.hours.sunday').split(':')[0]}:</strong> {t('contact.hours.sunday').split(':')[1]?.trim()}
                </p>
                <p className="text-xs sm:text-sm text-gray-500 mt-4 sm:mt-6">
                  {t('contact.hours.note')}
                </p>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Contact;