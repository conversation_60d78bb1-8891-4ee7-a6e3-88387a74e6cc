import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Car, Calendar, MapPin, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { sendCarRentalEmail } from '@/services/emailService';

/**
 * Car Rental Inquiry Modal Component
 * Allows users to submit car rental inquiries that are sent via <NAME_EMAIL>
 */
const CarRentalModal = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  // Form state management
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    pickupLocation: '',
    dropoffLocation: '',
    pickupDate: '',
    dropoffDate: '',
    vehicleType: 'sedan',
    additionalInfo: '',
  });

  // Loading state for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);

  /**
   * Handle input changes and update form state
   */
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  /**
   * Validate form data before submission
   */
  const validateForm = () => {
    if (!formData.name.trim()) {
      toast({
        title: t('modals.carRental.validation.name'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!formData.email.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      toast({
        title: t('modals.carRental.validation.email'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!formData.phone.trim()) {
      toast({
        title: t('modals.carRental.validation.phone'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!formData.pickupLocation.trim()) {
      toast({
        title: t('modals.carRental.validation.pickupLocation'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!formData.pickupDate) {
      toast({
        title: t('modals.carRental.validation.pickupDate'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    return true;
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Set loading state
    setIsSubmitting(true);

    try {
      // Send email via EmailJS
      await sendCarRentalEmail(formData);

      // Show success message
      toast({
        title: t('modals.carRental.toasts.successTitle'),
        description: t('modals.carRental.toasts.successDesc'),
        duration: 5000,
      });

      // Reset form and close modal
      setFormData({
        name: '',
        email: '',
        phone: '',
        pickupLocation: '',
        dropoffLocation: '',
        pickupDate: '',
        dropoffDate: '',
        vehicleType: 'sedan',
        additionalInfo: '',
      });
      onClose();
    } catch (error) {
      // Show error message
      toast({
        title: t('modals.carRental.toasts.errorTitle'),
        description: t('modals.carRental.toasts.errorDesc'),
        variant: 'destructive',
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Don't render if modal is not open
  if (!isOpen) return null;

  return (
    // Modal overlay with backdrop
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
      {/* Modal container with responsive width */}
      <div className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white rounded-2xl shadow-2xl">
        {/* Modal header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between rounded-t-2xl">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-[#273272]/10 flex items-center justify-center">
              <Car className="w-5 h-5 text-[#273272]" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900">{t('modals.carRental.title')}</h2>
          </div>
          {/* Close button - touch-friendly size */}
          <button
            onClick={onClose}
            className="w-10 h-10 rounded-full hover:bg-gray-100 flex items-center justify-center transition-colors"
            aria-label="Close modal"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Modal body with form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-5">
          {/* Personal Information Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">{t('modals.carRental.personalInfo')}</h3>
            
            {/* Name field */}
            <div>
              <Label htmlFor="name" className="text-gray-700 mb-2 block font-medium">
                {t('modals.carRental.labels.name')}
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder={t('modals.carRental.placeholders.name')}
                className="bg-gray-50 border-gray-300 text-gray-800 h-11"
                required
              />
            </div>

            {/* Email and Phone in grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.carRental.labels.email')}
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder={t('modals.carRental.placeholders.email')}
                  className="bg-gray-50 border-gray-300 text-gray-800 h-11"
                  required
                />
              </div>
              <div>
                <Label htmlFor="phone" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.carRental.labels.phone')}
                </Label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder={t('modals.carRental.placeholders.phone')}
                  className="bg-gray-50 border-gray-300 text-gray-800 h-11"
                  required
                />
              </div>
            </div>
          </div>

          {/* Rental Details Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">{t('modals.carRental.rentalDetails')}</h3>
            
            {/* Pickup and Dropoff locations */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="pickupLocation" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.carRental.labels.pickupLocation')}
                </Label>
                <div className="relative">
                  <Input
                    id="pickupLocation"
                    name="pickupLocation"
                    value={formData.pickupLocation}
                    onChange={handleChange}
                    placeholder={t('modals.carRental.placeholders.pickupLocation')}
                    className="bg-gray-50 border-gray-300 text-gray-800 h-11 pl-10"
                    required
                  />
                  <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
              </div>
              <div>
                <Label htmlFor="dropoffLocation" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.carRental.labels.dropoffLocation')}
                </Label>
                <div className="relative">
                  <Input
                    id="dropoffLocation"
                    name="dropoffLocation"
                    value={formData.dropoffLocation}
                    onChange={handleChange}
                    placeholder={t('modals.carRental.placeholders.dropoffLocation')}
                    className="bg-gray-50 border-gray-300 text-gray-800 h-11 pl-10"
                  />
                  <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>

            {/* Pickup and Dropoff dates */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="pickupDate" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.carRental.labels.pickupDate')}
                </Label>
                <div className="relative">
                  <Input
                    id="pickupDate"
                    name="pickupDate"
                    type="datetime-local"
                    value={formData.pickupDate}
                    onChange={handleChange}
                    className="bg-gray-50 border-gray-300 text-gray-800 h-11"
                    required
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="dropoffDate" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.carRental.labels.dropoffDate')}
                </Label>
                <div className="relative">
                  <Input
                    id="dropoffDate"
                    name="dropoffDate"
                    type="datetime-local"
                    value={formData.dropoffDate}
                    onChange={handleChange}
                    className="bg-gray-50 border-gray-300 text-gray-800 h-11"
                  />
                </div>
              </div>
            </div>

            {/* Vehicle type selection */}
            <div>
              <Label htmlFor="vehicleType" className="text-gray-700 mb-2 block font-medium">
                {t('modals.carRental.labels.vehicleType')}
              </Label>
              <select
                id="vehicleType"
                name="vehicleType"
                value={formData.vehicleType}
                onChange={handleChange}
                className="w-full h-11 px-3 bg-gray-50 border border-gray-300 rounded-md text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#273272]"
              >
                <option value="sedan">Sedan</option>
                <option value="suv">SUV</option>
                <option value="luxury">Luxury Vehicle</option>
                <option value="van">Van/Minibus</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Additional information textarea */}
            <div>
              <Label htmlFor="additionalInfo" className="text-gray-700 mb-2 block font-medium">
                {t('modals.carRental.labels.additionalInfo')}
              </Label>
              <textarea
                id="additionalInfo"
                name="additionalInfo"
                value={formData.additionalInfo}
                onChange={handleChange}
                placeholder={t('modals.carRental.placeholders.additionalInfo') || ' '}
                rows="3"
                className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#273272] resize-none"
              />
            </div>
          </div>

          {/* Submit button with loading state */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              onClick={onClose}
              variant="outline"
              className="px-6 h-11"
              disabled={isSubmitting}
            >
              {t('modals.carRental.buttons.cancel')}
            </Button>
            <Button
              type="submit"
              className="bg-[#273272] hover:bg-[#1e2558] text-white px-8 h-11 min-w-[120px]"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {t('modals.carRental.buttons.sending')}
                </>
              ) : (
                t('modals.carRental.buttons.send')
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CarRentalModal;

