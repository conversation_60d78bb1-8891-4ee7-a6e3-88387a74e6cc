import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X, FileText, Calendar, Globe, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { sendVisaConsultationEmail } from '@/services/emailService';

/**
 * Visa Consultation Inquiry Modal Component
 * Allows users to submit visa consultation inquiries that are sent via <NAME_EMAIL>
 */
const VisaConsultationModal = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  // Form state management
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    destinationCountry: '',
    visaType: 'tourist',
    travelDate: '',
    additionalInfo: '',
  });

  // Loading state for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);

  /**
   * Handle input changes and update form state
   */
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  /**
   * Validate form data before submission
   */
  const validateForm = () => {
    if (!formData.name.trim()) {
      toast({
        title: t('modals.visaConsultation.validation.name'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!formData.email.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      toast({
        title: t('modals.visaConsultation.validation.email'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!formData.phone.trim()) {
      toast({
        title: t('modals.visaConsultation.validation.phone'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!formData.destinationCountry.trim()) {
      toast({
        title: t('modals.visaConsultation.validation.destinationCountry'),
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    return true;
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Set loading state
    setIsSubmitting(true);

    try {
      // Send email via EmailJS
      await sendVisaConsultationEmail(formData);

      // Show success message
      toast({
        title: t('modals.visaConsultation.toasts.successTitle'),
        description: t('modals.visaConsultation.toasts.successDesc'),
        duration: 5000,
      });

      // Reset form and close modal
      setFormData({
        name: '',
        email: '',
        phone: '',
        destinationCountry: '',
        visaType: 'tourist',
        travelDate: '',
        additionalInfo: '',
      });
      onClose();
    } catch (error) {
      // Show error message
      toast({
        title: t('modals.visaConsultation.toasts.errorTitle'),
        description: t('modals.visaConsultation.toasts.errorDesc'),
        variant: 'destructive',
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Don't render if modal is not open
  if (!isOpen) return null;

  return (
    // Modal overlay with backdrop
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
      {/* Modal container with responsive width */}
      <div className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white rounded-2xl shadow-2xl">
        {/* Modal header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between rounded-t-2xl">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-[#14597d]/10 flex items-center justify-center">
              <FileText className="w-5 h-5 text-[#14597d]" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900">{t('modals.visaConsultation.title')}</h2>
          </div>
          {/* Close button - touch-friendly size */}
          <button
            onClick={onClose}
            className="w-10 h-10 rounded-full hover:bg-gray-100 flex items-center justify-center transition-colors"
            aria-label="Close modal"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Modal body with form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-5">
          {/* Personal Information Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">{t('modals.visaConsultation.personalInfo')}</h3>
            
            {/* Name field */}
            <div>
              <Label htmlFor="name" className="text-gray-700 mb-2 block font-medium">
                {t('modals.visaConsultation.labels.name')}
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder={t('modals.visaConsultation.placeholders.name')}
                className="bg-gray-50 border-gray-300 text-gray-800 h-11"
                required
              />
            </div>

            {/* Email and Phone in grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.visaConsultation.labels.email')}
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder={t('modals.visaConsultation.placeholders.email')}
                  className="bg-gray-50 border-gray-300 text-gray-800 h-11"
                  required
                />
              </div>
              <div>
                <Label htmlFor="phone" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.visaConsultation.labels.phone')}
                </Label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder={t('modals.visaConsultation.placeholders.phone')}
                  className="bg-gray-50 border-gray-300 text-gray-800 h-11"
                  required
                />
              </div>
            </div>
          </div>

          {/* Visa Details Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">{t('modals.visaConsultation.details')}</h3>
            
            {/* Destination Country */}
            <div>
              <Label htmlFor="destinationCountry" className="text-gray-700 mb-2 block font-medium">
                {t('modals.visaConsultation.labels.destinationCountry')}
              </Label>
              <div className="relative">
                <Input
                  id="destinationCountry"
                  name="destinationCountry"
                  value={formData.destinationCountry}
                  onChange={handleChange}
                  placeholder={t('modals.visaConsultation.placeholders.destinationCountry')}
                  className="bg-gray-50 border-gray-300 text-gray-800 h-11 pl-10"
                  required
                />
                <Globe className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>

            {/* Visa Type and Travel Date */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="visaType" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.visaConsultation.labels.visaType')}
                </Label>
                <select
                  id="visaType"
                  name="visaType"
                  value={formData.visaType}
                  onChange={handleChange}
                  className="w-full h-11 px-3 bg-gray-50 border border-gray-300 rounded-md text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#14597d]"
                  required
                >
                  <option value="tourist">Tourist Visa</option>
                  <option value="business">Business Visa</option>
                  <option value="student">Student Visa</option>
                  <option value="work">Work Visa</option>
                  <option value="transit">Transit Visa</option>
                  <option value="family">Family/Spouse Visa</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <Label htmlFor="travelDate" className="text-gray-700 mb-2 block font-medium">
                  {t('modals.visaConsultation.labels.travelDate')}
                </Label>
                <div className="relative">
                  <Input
                    id="travelDate"
                    name="travelDate"
                    type="date"
                    value={formData.travelDate}
                    onChange={handleChange}
                    className="bg-gray-50 border-gray-300 text-gray-800 h-11"
                  />
                </div>
              </div>
            </div>

            {/* Additional information textarea */}
            <div>
              <Label htmlFor="additionalInfo" className="text-gray-700 mb-2 block font-medium">
                {t('modals.visaConsultation.labels.additionalInfo')}
              </Label>
              <textarea
                id="additionalInfo"
                name="additionalInfo"
                value={formData.additionalInfo}
                onChange={handleChange}
                placeholder={t('modals.visaConsultation.placeholders.additionalInfo') || ' '}
                rows="4"
                className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#14597d] resize-none"
              />
            </div>
          </div>

          {/* Information note */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> Our visa experts will review your inquiry and contact you within 24 hours to discuss your specific requirements and guide you through the application process.
            </p>
          </div>

          {/* Submit button with loading state */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              onClick={onClose}
              variant="outline"
              className="px-6 h-11"
              disabled={isSubmitting}
            >
              {t('modals.visaConsultation.buttons.cancel')}
            </Button>
            <Button
              type="submit"
              className="bg-[#14597d] hover:bg-[#0f4460] text-white px-8 h-11 min-w-[120px]"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {t('modals.visaConsultation.buttons.sending')}
                </>
              ) : (
                t('modals.visaConsultation.buttons.send')
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VisaConsultationModal;

