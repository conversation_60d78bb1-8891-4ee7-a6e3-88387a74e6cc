import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { Calendar, User, ArrowLeftRight, Briefcase, Hotel, ShieldCheck, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import ServiceDetails from '@/components/ServiceDetails';
import { initEmailJS, sendFlightBookingEmail } from '@/services/emailService';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import '@/styles/datepicker-custom.css';
import { useTranslation } from 'react-i18next';


const Services = () => {
  // Initialize EmailJS on component mount
  useEffect(() => {
    initEmailJS();
  }, []);

  const { t } = useTranslation();

  // State for trip type and form data
  const [tripType, setTripType] = useState('one-way');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [flightFormData, setFlightFormData] = useState({
    from: '',
    to: '',
    departureDate: null, // Changed to null for DatePicker
    returnDate: null, // Changed to null for DatePicker
    passengers: '2',
  });

  const benefits = [
    {
      icon: Briefcase,
      title: 'For Business or Leisure',
      description: 'We cater to all your travel needs, whether for corporate trips or family vacations.',
    },
    {
      icon: Hotel,
      title: 'One-Stop Travel Shop',
      description: 'Book flights, hotels, car rentals, and more, all in one place.',
    },
    {
      icon: ShieldCheck,
      title: 'Tried and Trusted',
      description: 'We work with professional partners and have 24/7 support for your peace of mind.',
    },
  ];

  /**
   * Handle input changes for flight booking form
   */
  const handleFlightFormChange = (e) => {
    const { name, value } = e.target;
    setFlightFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  /**
   * Validate flight booking form
   */
  const validateFlightForm = () => {
    if (!flightFormData.from.trim()) {
      toast({
        title: 'Departure location is required',
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!flightFormData.to.trim()) {
      toast({
        title: 'Destination is required',
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (!flightFormData.departureDate) {
      toast({
        title: 'Departure date is required',
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    if (tripType === 'return' && !flightFormData.returnDate) {
      toast({
        title: 'Return date is required for return flights',
        variant: 'destructive',
        duration: 3000,
      });
      return false;
    }

    return true;
  };

  /**
   * Handle flight booking form submission
   */
  const handleSearch = async (e) => {
    e.preventDefault();

    // Validate form
    if (!validateFlightForm()) {
      return;
    }

    // Set loading state
    setIsSubmitting(true);

    try {
      // Prepare data for email - format dates for email
      const emailData = {
        from: flightFormData.from,
        to: flightFormData.to,
        departureDate: flightFormData.departureDate ? flightFormData.departureDate.toLocaleDateString('en-US', { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' }) : '',
        returnDate: tripType === 'return' && flightFormData.returnDate ? flightFormData.returnDate.toLocaleDateString('en-US', { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' }) : null,
        tripType: tripType === 'one-way' ? 'One-way' : 'Return',
        passengers: flightFormData.passengers,
      };

      // Send email via EmailJS
      await sendFlightBookingEmail(emailData);

      // Show success message
      toast({
        title: 'Flight Inquiry Sent Successfully! ✈️',
        description: 'Our team will contact you shortly with available options and pricing.',
        duration: 5000,
      });

      // Reset form
      setFlightFormData({
        from: '',
        to: '',
        departureDate: null,
        returnDate: null,
        passengers: '2',
      });
    } catch (error) {
      // Show error message
      toast({
        title: 'Failed to Send Inquiry',
        description: 'Please try again or contact us <NAME_EMAIL>',
        variant: 'destructive',
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>Our Services - Aircraft Rental, Travel Booking, Visa & Immigration | Sparkmars</title>
        <meta name="description" content="Explore Sparkmars comprehensive services: premium aircraft rental, flight and travel booking, expert visa consultation, and professional immigration services." />
      </Helmet>

      {/* Main container with responsive padding */}
      <div className="pt-0 bg-white text-gray-800">
        {/* Hero/Search section with responsive spacing */}
        <section className="py-12 sm:py-14 md:py-16 bg-white">
          <div className="max-w-5xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {/* Responsive headings */}
              <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-2 sm:mb-3">
                Find the right service for your journey
              </h1>
              <p className="text-sm sm:text-base md:text-lg text-gray-600 mb-6 sm:mb-8">
                From private jets to immigration advice, we've got you covered.
              </p>

              {/* Radio group with responsive spacing */}
              <RadioGroup defaultValue="one-way" className="flex flex-wrap gap-4 sm:gap-6 mb-5 sm:mb-6" onValueChange={setTripType}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="one-way" id="one-way" />
                  <Label htmlFor="one-way" className="text-sm sm:text-base cursor-pointer">{t('home.flightBooking.oneWay')}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="return" id="return" />
                  <Label htmlFor="return" className="text-sm sm:text-base cursor-pointer">{t('home.flightBooking.roundTrip')}</Label>
                </div>
              </RadioGroup>

              {/* Search form with mobile-first responsive design */}
              <form onSubmit={handleSearch}>
                <div className="bg-[#11a7ed] p-0.5 sm:p-1 rounded-lg shadow-lg">
                  {/* Mobile: stack vertically, Tablet+: grid layout */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-12 gap-0 sm:gap-px bg-white rounded-md">
                    {/* Pick-up location */}
                    <div className="relative sm:col-span-1 md:col-span-3">
                      <Input
                        type="text"
                        name="from"
                        value={flightFormData.from}
                        onChange={handleFlightFormChange}
                        placeholder={t('home.flightBooking.fromPlaceholder')}
                        className="w-full h-12 sm:h-14 pl-4 pr-10 border-0 rounded-t-md sm:rounded-tl-md sm:rounded-tr-none md:rounded-l-md md:rounded-tr-none focus:ring-0 text-sm sm:text-base"
                        required
                      />
                      <ArrowLeftRight className="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                    </div>

                    {/* Destination */}
                    <div className="relative sm:col-span-1 md:col-span-3">
                      <Input
                        type="text"
                        name="to"
                        value={flightFormData.to}
                        onChange={handleFlightFormChange}
                        placeholder={t('home.flightBooking.toPlaceholder')}
                        className="w-full h-12 sm:h-14 pl-4 border-0 sm:rounded-tr-md md:rounded-none focus:ring-0 text-sm sm:text-base"
                        required
                      />
                    </div>

                    {/* Departure date with DatePicker */}
                    <div className="relative sm:col-span-1 md:col-span-2">
                      <DatePicker
                        selected={flightFormData.departureDate}
                        onChange={(date) => setFlightFormData(prev => ({ ...prev, departureDate: date }))}
                        minDate={new Date()}
                        dateFormat="EEE, MMM d, yyyy"
                        showYearDropdown
                        showMonthDropdown
                        scrollableYearDropdown
                        yearDropdownItemNumber={100}
                        placeholderText={t('home.flightBooking.departureDate')}
                        className="w-full h-12 sm:h-14 pl-10 border-0 sm:rounded-bl-md md:rounded-none focus:ring-0 text-sm sm:text-base bg-white"
                        wrapperClassName="w-full"
                        required
                      />
                      <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400 pointer-events-none" />
                    </div>

                    {/* Return date with DatePicker - conditional */}
                    {tripType === 'return' && (
                      <div className="relative sm:col-span-1 md:col-span-2">
                        <DatePicker
                          selected={flightFormData.returnDate}
                          onChange={(date) => setFlightFormData(prev => ({ ...prev, returnDate: date }))}
                          minDate={flightFormData.departureDate || new Date()}
                          dateFormat="EEE, MMM d, yyyy"
                          showYearDropdown
                          showMonthDropdown
                          scrollableYearDropdown
                          yearDropdownItemNumber={100}
                          placeholderText={t('home.flightBooking.returnDate')}
                          className="w-full h-12 sm:h-14 pl-10 border-0 sm:rounded-br-md md:rounded-none focus:ring-0 text-sm sm:text-base bg-white"
                          wrapperClassName="w-full"
                          required={tripType === 'return'}
                        />
                        <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400 pointer-events-none" />
                      </div>
                    )}

                    {/* Passengers */}
                    <div className={`relative ${tripType === 'return' ? 'sm:col-span-1' : 'sm:col-span-1'} md:col-span-1`}>
                      <Input
                        type="number"
                        name="passengers"
                        value={flightFormData.passengers}
                        onChange={handleFlightFormChange}
                        min="1"
                        max="20"
                        className="w-full h-12 sm:h-14 pl-10 border-0 focus:ring-0 text-sm sm:text-base"
                      />
                      <User className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                    </div>

                    {/* Inquire button with loading state */}
                    <div className={`${tripType === 'return' ? 'sm:col-span-1' : 'sm:col-span-1'} md:col-span-1`}>
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full h-12 sm:h-14 bg-[#19a6ec] hover:bg-[#1595d4] text-white font-bold text-base sm:text-lg rounded-b-md sm:rounded-br-md sm:rounded-bl-none md:rounded-r-md md:rounded-bl-none touch-manipulation disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          <span>{t('home.flightBooking.searchFlights')}</span>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </form>
            </motion.div>
          </div>
        </section>

        {/* Benefits section with responsive design */}
        <section className="py-12 sm:py-14 md:py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            {/* Benefits grid - responsive columns */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8 md:gap-10 lg:gap-12">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="flex items-start space-x-3 sm:space-x-4"
                >
                  {/* Icon with responsive sizing */}
                  <div className="flex-shrink-0">
                    <benefit.icon className="w-7 h-7 sm:w-8 sm:h-8 text-[#19a6ec]" />
                  </div>
                  {/* Text content with responsive sizing */}
                  <div>
                    <h3 className="text-base sm:text-lg font-bold text-gray-900 mb-1">{benefit.title}</h3>
                    <p className="text-sm sm:text-base text-gray-600 leading-relaxed">{benefit.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        <ServiceDetails />
      </div>
    </>
  );
};

export default Services;